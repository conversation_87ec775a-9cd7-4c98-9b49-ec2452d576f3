<template>
  <div class="llm-message-container">
    <div class="avatar-container">
      <IconDeepseekChatAvatar class="llm-avatar" />
    </div>
    <div class="message-content-area">
      <div class="message-text">
        <Md :markdown="message" />
      </div>
      <div class="message-actions-toolbar" v-if="!pending && message">
        <VersionController v-if="showVersionController" v-bind="props.versions" @change-version="handleVersionChange" />
        <div class="action-button copy-button" @click="copyMessage">
          <div class="action-icon-wrapper">
            <IconCopy />
          </div>
        </div>
        <div class="action-button regenerate-button" @click="regenerateMessage">
          <div class="action-icon-wrapper">
            <IconRegenerate />
          </div>
        </div>
        <div class="action-button like-button" @click="likeMessage">
          <div class="action-icon-wrapper">
            <IconLike />
          </div>
        </div>
        <div class="action-button dislike-button" @click="dislikeMessage">
          <div class="action-icon-wrapper">
            <IconDislike />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { PropType } from 'vue';
import IconDeepseekChatAvatar from '../icons/IconDeepseekChatAvatar.vue';
import Md from './md.vue';
import IconCopy from '../icons/IconCopy.vue';
import IconRegenerate from '../icons/IconRegenerate.vue';
import IconLike from '../icons/IconLike.vue';
import IconDislike from '../icons/IconDislike.vue';
import VersionController from './version-controller.vue';

interface VersionControllerProps {
  current: number;
  total: number;
  loading?: boolean;
}

// Define props
const props = defineProps({
  message: String,
  pending: Boolean,
  versions: {
    type: Object as PropType<VersionControllerProps>,
    required: false
  }
});

const showVersionController = computed(() => {
  return props.versions && props.versions.total > 1;
});

// --- Placeholder Action Handlers ---
// You'll need to implement the actual logic for these actions,
// likely by emitting events to the parent component.
const copyMessage = () => {
  console.log('Copy action triggered');
  // Example: navigator.clipboard.writeText(props.message || '');
  // Example emit: emit('copy', props.message);
};

const regenerateMessage = () => {
  console.log('Regenerate action triggered');
  // Example emit: emit('regenerate');
};

const likeMessage = () => {
  console.log('Like action triggered');
  // Example emit: emit('feedback', { rating: 'like' });
};

const dislikeMessage = () => {
  console.log('Dislike action triggered');
  // Example emit: emit('feedback', { rating: 'dislike' });
};

const emit = defineEmits(['change-version', 'copy', 'regenerate', 'feedback']);

const handleVersionChange = (newVersion) => {
  emit('change-version', newVersion);
};
</script>

<style lang="scss" scoped> // Added 'scoped' for better style encapsulation

// Main container for the entire message row (avatar + content)
.llm-message-container {
  display: flex;
  flex-direction: row;
  gap: 10px;
  padding: 10px 0; // Add some padding for spacing between messages
}

// Container for the avatar
.avatar-container {
  flex-shrink: 0; // Prevent avatar from shrinking
}

// Avatar specific styles
.llm-avatar {
  box-sizing: content-box;
  width: 32px;
  height: 32px;
  padding: 3px;
  border-radius: 50%; // Use 50% for a perfect circle
  border: 1px solid #d5e4ff;
  display: block; // Prevents potential extra space below image
}

// Container for the message text and the actions toolbar
.message-content-area {
  display: flex;
  flex-direction: column; // Stack message text and toolbar vertically
  gap: 8px; // Space between message text and toolbar
  flex-grow: 1; // Allow this area to take up remaining space
  min-width: 0; // Prevent overflow issues in flex layouts
}

// Styling for the message text block
.message-text {
  text-align: left;
  // Add styles for code blocks, etc., if needed within the Md component or here
  // e.g., :deep(pre) { ... }
}

// --- Toolbar Styles ---

// Container for the action buttons
.message-actions-toolbar {
  display: flex;
  align-items: center;
  gap: 10px; // Space between buttons
  opacity: 0.7; // Slightly transparent initially
  transition: opacity 0.2s ease-in-out;

  // Show toolbar more clearly on hover of the message area (optional)
  // .llm-message-container:hover & {
  //   opacity: 1;
  // }
}

// Individual action button style
.action-button {
  display: flex; // Use flex to center the icon wrapper
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: rgb(73, 73, 73); // Icon color (rgb(144, 144, 144))
  cursor: pointer;
  position: relative; // Needed if using tooltips or other positioned elements later
  border-radius: 4px; // Slightly rounded corners for the button area
  transition: background-color 0.2s ease, color 0.2s ease;
  padding: 2px;
  box-sizing: content-box;

  &:hover {
    color: #404040; // Darker icon on hover (rgb(64, 64, 64))
    background-color: #f5f5f5; // Slight background highlight on hover
  }

  &:focus {
    outline: 2px solid dodgerblue; // Basic focus indicator for accessibility
    outline-offset: 1px;
  }
  &:focus:not(:focus-visible) {
     outline: none; // Remove outline if focused by mouse, keep for keyboard
  }
}

// Wrapper div inside the button, mainly for sizing the icon
.action-icon-wrapper {
  display: flex; // Helps align the SVG if it has weird internal padding
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  line-height: 0; // Prevent potential line-height issues with icons

  // Style the SVG icon itself (if needed and not handled by the component)
  // This targets the SVG element directly within the wrapper
  :deep(svg) {
    width: 18px; // Slightly smaller than the button area for padding
    height: 18px;
    fill: currentColor; // Use the color set on the parent (.action-button)
  }
}

</style>
/**
 * @fileoverview 用于 LLM 聊天记录的 TypeScript 定义，采用基于节点的图结构，支持对话分支。
 */

/**
 * 表示与 LLM 生成的消息或用户消息相关的元数据。
 */
interface MessageMetadata {
  /** 如果消息来自 assistant，则为所使用的模型，例如 "model-a"。 */
  model?: string;
  /** 用户对 assistant 消息的反馈，例如 "good", "bad"。 */
  feedback?: string;
  /** 对于用户消息，可以包含编辑来源、草稿状态等。 */
  edit_source?: string;
  /** 允许其他任意元数据。 */
  [key: string]: any;
}

/**
 * 代表对话图中的一个“消息节点”。
 * 每个节点代表用户的一次输入或 LLM 的一次回应。
 * 编辑或重新生成会创建新的节点。
 */
interface ChatMessageNode {
  /** 此消息节点的唯一标识符。 */
  node_id: string;
  /** 消息节点的角色，可以是 "user" 或 "assistant"。 */
  role: "user" | "assistant";
  /**
   * 此消息节点的文本内容。
   */
  content: string;
  /**
   * ISO date-time string 指示此消息节点的创建时间。
   */
  timestamp: string;
  /**
   * 关于此消息节点的可选元数据。
   * - 如果 `role` 是 "assistant"，可以包含模型信息、生成参数、用户反馈等。
   * - 如果 `role` 是 "user"，可以包含编辑来源、草稿状态等。
   */
  metadata?: MessageMetadata;
  /**
   * 一个包含后续可能消息节点的 `node_id` 的数组。
   * 这用于表示对话的分支。例如，一个用户消息可能有多个 LLM 回应分支，
   * 或者一个 LLM 回应后，用户可能有多种不同的后续提问。
   * 如果一个节点是叶子节点（当前没有后续消息），则此数组为空。
   */
  next_node_ids: string[];

  active_next_node_id: string;
}

/**
 * 表示一个完整的聊天会话。
 */
interface ChatSession {
  /** Chat 会话的唯一标识符。 */
  chat_id: string;
  /** Chat 会话的可选标题。 */
  title?: string;
  /** ISO date-time string 指示 Chat 会话的创建时间。 */
  creation_timestamp: string;
  /** ISO date-time string 指示 Chat 会话的最后更新时间。 */
  last_update_timestamp: string;
  /**
   * 包含此聊天会话中所有消息节点的列表。
   * 这些节点共同构成一个对话图。
   * 客户端需要通过 `previous_node_id` 和 `next_node_ids` 来遍历和呈现特定的对话路径或分支。
   * 通常，对话从 `previous_node_id` 为 `null` 的节点开始。
   */
  message_nodes: ChatMessageNode[];
  entry_node_id: string;
}
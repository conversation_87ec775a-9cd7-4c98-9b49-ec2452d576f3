## 前端修改方案 (Vue.js/TypeScript)

**背景**:

您当前的前端 Vue.js 组件（可能使用 Composition API 和 TypeScript）已经有一个 `ChatSession` 和 `ChatMessageNode` 的 TypeScript 定义，并且能够通过遍历 `active_next_node_id` 来渲染一个活动对话路径。它还有一个 `handleVersionChange` 方法用于在同一级别的不同分支间切换。

后端 API 即将更新以支持持久化的消息分支和版本控制。前端需要调用这些新的或更新后的 API 来：

1.  创建、列出和打开聊天会话 (`ChatSession`)。
2.  发送新消息（在当前活动路径上继续）。
3.  编辑用户消息（这将通过后端 API 创建一个新的分支）。
4.  请求重新生成助手的回应（这也将通过后端 API 创建一个新的分支）。
5.  当用户通过 UI 切换消息版本时，`handleVersionChange` 需要调用后端 API 来持久化这个 `active_next_node_id` 的变更。

**目标**: 修改前端 Vue 组件和相关服务，以与新的后端 API 对接，并正确管理和展示可分支的聊天数据。

---

### 1. API 服务层 (例如 `src/services/apiService.ts`)

创建一个专门的 TypeScript 文件来封装所有与后端聊天 API 的交互。

```typescript
// src/services/apiService.ts

// Re-using your existing TypeScript definitions for ChatSession and ChatMessageNode
// Ensure these are imported or defined here if not globally available.
export interface MessageMetadata {
  model?: string;
  feedback?: string;
  edit_source?: string;
  [key: string]: any;
}

export interface ChatMessageNode {
  node_id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
  metadata?: MessageMetadata;
  next_node_ids: string[];
  active_next_node_id: string | null; // Allow null if it's a leaf with no active next yet
}

export interface ChatSession {
  id: string; // Changed from chat_id to id to match backend response for Conversation.id
  title?: string;
  creation_timestamp: string; // Assuming this maps to created_at
  last_update_timestamp: string; // Assuming this maps to updated_at
  entry_node_id: string | null; // Allow null for new chats
  message_nodes: ChatMessageNode[];
}

// For listing chats - backend sends less data
export interface ChatSessionMetadata {
  id: string;
  title?: string;
  created_at: string;
  updated_at: string;
  entry_node_id: string | null;
}

export interface MessageTurnPayload {
  origin_node_id: string | null;
  action_type: 'new_message' | 'edit_user_message' | 'regenerate_assistant_message';
  user_content?: string; // Optional because regenerate doesn't need it
  metadata?: Record<string, any>; // Optional metadata for user message
}

export interface MessageTurnResponse {
  new_user_node: ChatMessageNode | null; // Null if regenerate
  new_assistant_node: ChatMessageNode;
  updated_origin_node_links: ChatMessageNode | null; // Contains updated links of the origin_node
  chat_session_meta_update: {
    id: string;
    updated_at: string;
    entry_node_id: string | null;
  };
}

const API_BASE_URL = '/api'; // Adjust if your Flask prefix is different

async function fetchApi<T>(url: string, options: RequestInit = {}): Promise<T> {
  const headers = {
    'Content-Type': 'application/json',
    // Add Authorization header if needed, e.g., for JWT
    // 'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
    ...options.headers,
  };
  const response = await fetch(`${API_BASE_URL}${url}`, { ...options, headers });
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: response.statusText }));
    throw new Error(`API Error (${response.status}): ${errorData.message || 'Unknown error'}`);
  }
  if (response.status === 204) { // No Content
    return undefined as T;
  }
  return response.json() as T;
}

export const chatApiService = {
  createChat: (title: string = 'New Chat'): Promise<ChatSession> => {
    return fetchApi<ChatSession>('/chats', {
      method: 'POST',
      body: JSON.stringify({ title }),
    });
  },

  getChats: (): Promise<ChatSessionMetadata[]> => {
    return fetchApi<ChatSessionMetadata[]>('/chats');
  },

  getChatDetails: (chatId: string): Promise<ChatSession> => {
    return fetchApi<ChatSession>(`/chats/${chatId}`);
  },

  deleteChat: (chatId: string): Promise<{ message: string }> => {
    return fetchApi<{ message: string }>(`/chats/${chatId}`, {
      method: 'DELETE',
    });
  },

  renameChat: (chatId: string, newTitle: string): Promise<{ message: string, id: string, new_title: string }> => {
    return fetchApi<{ message: string, id: string, new_title: string }>(`/chats/${chatId}/rename`, {
      method: 'PUT',
      body: JSON.stringify({ title: newTitle }),
    });
  },

  postMessageTurn: (chatId: string, payload: MessageTurnPayload): Promise<MessageTurnResponse> => {
    return fetchApi<MessageTurnResponse>(`/chats/${chatId}/message_turns`, {
      method: 'POST',
      body: JSON.stringify(payload),
    });
  },

  setActiveBranch: (chatId: string, nodeId: string, activeNextNodeId: string): Promise<{ message: string, updated_node_id: string, new_active_next_node_id: string }> => {
    return fetchApi<{ message: string, updated_node_id: string, new_active_next_node_id: string }>(`/chats/${chatId}/nodes/${nodeId}/set_active_branch`, {
      method: 'PUT',
      body: JSON.stringify({ active_next_node_id: activeNextNodeId }),
    });
  },
};

```
**Notes for `apiService.ts`**:
* The `ChatSession` interface's `id` field now matches the backend's `Conversation.id` (which is a string in the API response). Timestamps are assumed to be ISO strings.
* Error handling in `WorkspaceApi` is basic; enhance as needed.
* Authentication (e.g., JWT tokens) should be handled in `WorkspaceApi` if required.

---

### 2. Vue Component Modifications (e.g., `src/components/ChatView.vue`)

Assume your main chat component looks something like the provided Vue template.

```html
<template>
  <div class="chat-root" v-if="chat">
    <div class="title-bar">
      <input v-model="editableTitle" @blur="saveTitle" @keyup.enter="saveTitle" :disabled="isRenaming" />
      <button @click="confirmDeleteChat" :disabled="isDeleting">Delete Chat</button>
    </div>
    <div class="messages">
      <template v-for="message in displayedMessages" :key="message.node_id">
        <div class="message-item">
          <UserMessage
            v-if="message.role === 'user'"
            :message="message.content"
            :versions="getVersionInfo(message)"
            @change-version="(versionIndex) => handleVersionChange(message.node_id, versionIndex)"
            :node-id="message.node_id"
            @edit-message="promptEditUserMessage" 
          />
          <LlmMessage
            v-else
            :message="message.content"
            :versions="getVersionInfo(message)"
            @change-version="(versionIndex) => handleVersionChange(message.node_id, versionIndex)"
            :node-id="message.node_id"
            @regenerate-message="handleRegenerateAssistantMessage"
          />
        </div>
      </template>
      <div v-if="isLoading" class="loading-indicator">Loading...</div>
    </div>
    <div class="input-area">
      <textarea v-model="newMessageContent" @keyup.enter="sendMessage" placeholder="Type your message..."></textarea>
      <button @click="sendMessage" :disabled="isSending">Send</button>
    </div>
  </div>
  <div v-else-if="isLoadingChat">Loading chat...</div>
  <div v-else>Select or create a chat.</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, reactive } from 'vue';
import LlmMessage from './llm-message.vue'; // Assuming these are your message components
import UserMessage from './user-message.vue'; // Assuming these are your message components
import { chatApiService, type ChatSession, type ChatMessageNode, type MessageTurnPayload } from '@/services/apiService'; // Adjust path

interface DisplayMessage extends ChatMessageNode {
  // Potentially add more UI-specific fields if needed
  currentVersionIndex: number;
  versionCount: number;
}

// Props if this component takes a chatId, or manage active chatId internally
const props = defineProps<{
  activeChatId: string | null;
}>();

const chat = ref<ChatSession | null>(null);
const isLoading = ref(false); // For message sending/regen
const isLoadingChat = ref(false); // For loading initial chat data
const isSending = ref(false);
const isRenaming = ref(false);
const isDeleting = ref(false);

const newMessageContent = ref('');
const editableTitle = ref('');


// Helper to find a node by ID from the local chat.message_nodes
const findNodeById = (nodeId: string | null): ChatMessageNode | undefined => {
  if (!nodeId || !chat.value) return undefined;
  return chat.value.message_nodes.find(node => node.node_id === nodeId);
};

// This computed property generates the linear list of messages to display based on the active path
const displayedMessages = computed<DisplayMessage[]>(() => {
  if (!chat.value || !chat.value.entry_node_id) return [];

  const result: DisplayMessage[] = [];
  let currentNode = findNodeById(chat.value.entry_node_id);

  while (currentNode) {
    const versionCount = currentNode.next_node_ids?.length || 0;
    // If active_next_node_id is null or empty, it means there's no *active* next node chosen yet for this branch,
    // but it could still have multiple next_node_ids (potential versions).
    // If it's a true leaf (next_node_ids is empty), versionCount is 0 for its "next step".
    // The versioning UI typically applies to *choosing which of the next_node_ids to follow*.
    // So, versionCount here refers to how many options *this* node has for its *next* step.
    
    // For a UserMessage, versionCount is number of assistant responses.
    // For an AssistantMessage, versionCount is number of user follow-ups.
    
    // Let's rethink getVersionInfo and what it means for a message.
    // If a node N has N.next_node_ids = [A, B, C] and N.active_next_node_id = B,
    // then N offers 3 versions for its successor. The "current version" is B (index 1).
    // This is what your original `handleVersionChange` implies.

    const activeIndex = currentNode.active_next_node_id
      ? currentNode.next_node_ids.findIndex(id => id === currentNode.active_next_node_id)
      : -1; // -1 if no active next node or it's a leaf

    result.push({
      ...currentNode,
      currentVersionIndex: activeIndex, // 0-indexed
      versionCount: currentNode.next_node_ids?.length || 0,
    });

    if (!currentNode.active_next_node_id) break; // End of active path
    currentNode = findNodeById(currentNode.active_next_node_id);
  }
  return result;
});

function getVersionInfo(node: ChatMessageNode) {
  const versionCount = node.next_node_ids?.length || 0;
  const currentIndex = node.active_next_node_id
    ? node.next_node_ids.findIndex(id => id === node.active_next_node_id)
    : -1; // Current version (0-indexed)
  return { current: currentIndex + 1, total: versionCount }; // 1-indexed for UI
}


watch(() => props.activeChatId, async (newId, oldId) => {
  if (newId) {
    await loadChatDetails(newId);
  } else {
    chat.value = null;
    editableTitle.value = '';
  }
}, { immediate: true });

watch(chat, (newChat) => {
  if (newChat) {
    editableTitle.value = newChat.title || '';
  }
}, { deep: true });


async function loadChatDetails(chatId: string) {
  isLoadingChat.value = true;
  try {
    const chatData = await chatApiService.getChatDetails(chatId);
    chat.value = chatData;
  } catch (error) {
    console.error("Failed to load chat details:", error);
    // Show error to user
  } finally {
    isLoadingChat.value = false;
  }
}

async function saveTitle() {
  if (!chat.value || chat.value.title === editableTitle.value.trim()) return;
  isRenaming.value = true;
  try {
    await chatApiService.renameChat(chat.value.id, editableTitle.value.trim());
    if (chat.value) { // Check again, might have changed
        chat.value.title = editableTitle.value.trim(); // Optimistic update or use response
        // Backend also updates updated_at, which we're not explicitly getting back here.
        // A full reload (loadChatDetails) or specific update would be more robust.
    }
  } catch (error) {
    console.error("Failed to rename chat:", error);
    editableTitle.value = chat.value?.title || ''; // Revert on error
  } finally {
    isRenaming.value = false;
  }
}

async function confirmDeleteChat() {
    if(!chat.value) return;
    if(confirm(`Are you sure you want to delete "${chat.value.title}"? This cannot be undone.`)){
        isDeleting.value = true;
        try {
            await chatApiService.deleteChat(chat.value.id);
            // TODO: Emit event to parent to clear activeChatId or select another chat
            chat.value = null; 
            alert("Chat deleted successfully.");
        } catch (error) {
            console.error("Failed to delete chat:", error);
            alert("Error deleting chat.");
        } finally {
            isDeleting.value = false;
        }
    }
}

// Centralized function to update local chat state from API response
function updateChatStateFromResponse(response: MessageTurnResponse) {
  if (!chat.value) return;

  const { new_user_node, new_assistant_node, updated_origin_node_links, chat_session_meta_update } = response;

  // 1. Add new nodes
  if (new_user_node) {
    const existingUserNodeIndex = chat.value.message_nodes.findIndex(n => n.node_id === new_user_node.node_id);
    if (existingUserNodeIndex > -1) chat.value.message_nodes[existingUserNodeIndex] = new_user_node;
    else chat.value.message_nodes.push(new_user_node);
  }
  if (new_assistant_node) {
     const existingAssistantNodeIndex = chat.value.message_nodes.findIndex(n => n.node_id === new_assistant_node.node_id);
    if (existingAssistantNodeIndex > -1) chat.value.message_nodes[existingAssistantNodeIndex] = new_assistant_node;
    else chat.value.message_nodes.push(new_assistant_node);
  }

  // 2. Update links of the origin node (if any was affected and returned)
  if (updated_origin_node_links) {
    const originIndex = chat.value.message_nodes.findIndex(n => n.node_id === updated_origin_node_links.node_id);
    if (originIndex > -1) {
      chat.value.message_nodes[originIndex] = {
          ...chat.value.message_nodes[originIndex], // Keep other properties
          ...updated_origin_node_links // Override with new link info
      };
    } else {
        // This case (origin_node_links updated but node not in local list) should ideally not happen
        // if origin_node_id was correctly determined from local state.
        // Could push it if it's somehow a new "implicit" node from backend perspective.
        // For now, log a warning.
        console.warn("Updated origin_node_links for a node not found locally:", updated_origin_node_links);
    }
  }
  
  // 3. Update chat session metadata
  chat.value.last_update_timestamp = chat_session_meta_update.updated_at;
  if (chat_session_meta_update.entry_node_id && !chat.value.entry_node_id) {
    chat.value.entry_node_id = chat_session_meta_update.entry_node_id;
  } else if (chat_session_meta_update.entry_node_id && chat.value.entry_node_id !== chat_session_meta_update.entry_node_id) {
    // This could happen if an edit creates a new effective entry.
    // The UI should re-render based on the new entry_node_id.
    chat.value.entry_node_id = chat_session_meta_update.entry_node_id;
  }
}


async function sendMessage() {
  if (!newMessageContent.value.trim() || !chat.value) return;
  isSending.value = true;

  // Determine origin_node_id: last node in the current displayedMessages (active path)
  // If displayedMessages is empty, it's the first message, origin_node_id is null.
  const lastDisplayedNode = displayedMessages.value.length > 0 
    ? displayedMessages.value[displayedMessages.value.length - 1] 
    : null;
  
  const payload: MessageTurnPayload = {
    origin_node_id: lastDisplayedNode ? lastDisplayedNode.node_id : null,
    action_type: 'new_message',
    user_content: newMessageContent.value.trim(),
    metadata: { edit_source: "web_client_initial_prompt" } // Example metadata
  };

  try {
    const response = await chatApiService.postMessageTurn(chat.value.id, payload);
    updateChatStateFromResponse(response);
    newMessageContent.value = ''; // Clear input
  } catch (error) {
    console.error("Failed to send message:", error);
    // Show error to user
  } finally {
    isSending.value = false;
  }
}

// Called when user wants to edit their own message
// `targetUserNodeId` is the node_id of the user message component that emitted 'edit-message'
async function promptEditUserMessage(targetUserNodeId: string) {
  if (!chat.value) return;
  const targetNode = findNodeById(targetUserNodeId);
  if (!targetNode || targetNode.role !== 'user') {
    console.error("Cannot edit: Node not found or not a user node.");
    return;
  }

  const newContent = prompt("Enter new content for your message:", targetNode.content);
  if (newContent === null || newContent.trim() === "" || newContent.trim() === targetNode.content) {
    return; // User cancelled, entered empty, or no change
  }

  // Find the predecessor of targetUserNodeId to set as origin_node_id
  // This requires knowing the graph structure.
  let predecessorNodeId: string | null = null;
  if (chat.value.entry_node_id === targetUserNodeId) {
    predecessorNodeId = null; // Editing the very first message. Backend needs to handle this as a new entry point.
                              // The proposed backend for 'edit_user_message' expects origin_node_id to be the predecessor.
                              // If target is entry, this creates a conceptual challenge for "predecessor".
                              // For now, let's find the actual predecessor in the current active path.
                              // A more robust way is if the backend can take targetUserNodeId and figure out the branching itself.
                              // Assuming backend needs predecessor as origin_node_id:
    const path = displayedMessages.value; // Current active path
    const targetIndexInPath = path.findIndex(m => m.node_id === targetUserNodeId);
    if (targetIndexInPath > 0) {
        predecessorNodeId = path[targetIndexInPath - 1].node_id;
    } else if (targetIndexInPath === 0) { // targetNode is the entry_node_id
        predecessorNodeId = null; // For backend to know it's branching from root
    } else {
        console.error("Could not find predecessor for edit. Node may not be in active path.");
        return;
    }

  } else {
      // General case: find node P such that P.active_next_node_id = targetUserNodeId
      const pNode = chat.value.message_nodes.find(n => n.active_next_node_id === targetUserNodeId);
      if (pNode) {
          predecessorNodeId = pNode.node_id;
      } else {
          // Fallback or if node is not on an active main path but part of a non-active branch
          // For simplicity, try to find any node that lists it as a next_node_id.
          // This part of the logic is tricky and depends on how "origin_node_id" is defined for edits.
          // The backend design for "edit_user_message" states:
          // "origin_node_id is the node *preceding* the user message that was edited."
          const path = displayedMessages.value;
          const targetIndexInPath = path.findIndex(m => m.node_id === targetUserNodeId);
          if (targetIndexInPath > 0) {
              predecessorNodeId = path[targetIndexInPath - 1].node_id;
          } else if (targetIndexInPath === 0) {
              predecessorNodeId = null;
          } else {
              console.error("Failed to find predecessor for editing. The message might not be on the current active path or logic is incomplete.");
              alert("Error: Could not determine context for editing.");
              return;
          }
      }
  }


  isLoading.value = true; // General loading state
  const payload: MessageTurnPayload = {
    origin_node_id: predecessorNodeId,
    action_type: 'edit_user_message',
    user_content: newContent.trim(),
    metadata: { edit_source: "web_client_edit" }
  };

  try {
    const response = await chatApiService.postMessageTurn(chat.value.id, payload);
    updateChatStateFromResponse(response);
  } catch (error) {
    console.error("Failed to edit message:", error);
    alert("Error editing message.");
  } finally {
    isLoading.value = false;
  }
}


// Called when user wants to regenerate an assistant's response
// `targetAssistantNodeId` is the node_id of the LLMMessage component that emitted 'regenerate-message'
async function handleRegenerateAssistantMessage(targetAssistantNodeId: string) {
  if (!chat.value) return;
  const assistantNode = findNodeById(targetAssistantNodeId);
  if (!assistantNode || assistantNode.role !== 'assistant') {
    console.error("Cannot regenerate: Node not found or not an assistant node.");
    return;
  }

  // For 'regenerate_assistant_message', origin_node_id is the USER message
  // that PRECEDES this assistantNode.
  let userPredecessorNodeId: string | null = null;
  // Find node P such that P.role === 'user' and P.active_next_node_id === targetAssistantNodeId
  // OR P.next_node_ids.includes(targetAssistantNodeId) if we want to regenerate non-active one.
  // For simplicity, assume we are regenerating for the active path.
  const path = displayedMessages.value;
  const assistantIndexInPath = path.findIndex(m => m.node_id === targetAssistantNodeId);
  if (assistantIndexInPath > 0 && path[assistantIndexInPath - 1].role === 'user') {
    userPredecessorNodeId = path[assistantIndexInPath - 1].node_id;
  } else {
    // More robust search if not on active path or structure is complex
    const pNode = chat.value.message_nodes.find(n => n.role === 'user' && n.active_next_node_id === targetAssistantNodeId);
    if(pNode) {
        userPredecessorNodeId = pNode.node_id;
    } else {
        console.error("Failed to find user predecessor for regeneration.");
        alert("Error: Could not determine context for regeneration.");
        return;
    }
  }

  if (!userPredecessorNodeId) {
      console.error("Could not find the user message preceding this assistant message for regeneration.");
      return;
  }

  isLoading.value = true;
  const payload: MessageTurnPayload = {
    origin_node_id: userPredecessorNodeId, // This is the user message node.
    action_type: 'regenerate_assistant_message',
    // user_content is not needed for regenerate
  };

  try {
    const response = await chatApiService.postMessageTurn(chat.value.id, payload);
    updateChatStateFromResponse(response);
  } catch (error) {
    console.error("Failed to regenerate assistant message:", error);
    alert("Error regenerating response.");
  } finally {
    isLoading.value = false;
  }
}

// This function is called when user clicks version switcher on a message.
// node_id: the node whose active successor needs to change.
// newVersionIndex: 1-indexed version selected by user.
async function handleVersionChange(nodeIdToUpdateLinks: string, newVersionIndex: number) { // newVersionIndex is 1-based
  if (!chat.value) return;
  const nodeToUpdate = findNodeById(nodeIdToUpdateLinks);

  if (!nodeToUpdate || !nodeToUpdate.next_node_ids || nodeToUpdate.next_node_ids.length === 0) {
    console.warn("handleVersionChange called on a node with no versions or not found:", nodeIdToUpdateLinks);
    return;
  }

  const newActiveNextNodeId = nodeToUpdate.next_node_ids[newVersionIndex - 1]; // 0-indexed for array
  if (!newActiveNextNodeId || nodeToUpdate.active_next_node_id === newActiveNextNodeId) {
    return; // No change or invalid index
  }

  isLoading.value = true;
  try {
    await chatApiService.setActiveBranch(chat.value.id, nodeIdToUpdateLinks, newActiveNextNodeId);
    // Optimistic update:
    nodeToUpdate.active_next_node_id = newActiveNextNodeId;
    // The displayedMessages computed property will auto-update the view.
  } catch (error) {
    console.error("Failed to set active branch:", error);
    alert("Error switching version.");
  } finally {
    isLoading.value = false;
  }
}

</script>

<style lang="scss" scoped>
/* Your existing styles, potentially add: */
.title-bar {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ccc;
  input {
    flex-grow: 1;
    font-size: 1.2em;
    border: none;
    padding: 5px;
  }
  input:disabled {
    background-color: #f0f0f0;
  }
  button {
    margin-left: 10px;
  }
}
.loading-indicator, .input-area {
  padding: 10px;
  text-align: center;
}
.input-area {
  display: flex;
  textarea {
    flex-grow: 1;
    min-height: 50px;
    margin-right: 10px;
  }
}
.message-item {
  margin-bottom: 10px; /* Add some space between messages */
}
</style>
```

**UserMessage.vue / LlmMessage.vue Child Component Requirements:**
* They should emit events like `@edit-message="nodeId"` (from UserMessage) and `@regenerate-message="nodeId"` (from LlmMessage) when corresponding UI actions are triggered by the user.
* The `versions` prop and `@change-version` event seem to be correctly handled for version switching UI.

**Key Considerations for Frontend:**

* **Determining `origin_node_id`**: This is crucial.
    * For `'new_message'`: It's the `node_id` of the last message in the currently rendered `displayedMessages` path. If `displayedMessages` is empty, `origin_node_id` is `null`.
    * For `'edit_user_message'`: The backend expects `origin_node_id` to be the node *preceding* the user message being edited. The frontend needs to find this predecessor. If the message being edited is the `entry_node_id`, then `origin_node_id` would be `null` (or backend needs to handle `target_node_to_edit_id` instead). The provided example attempts to find the predecessor from the active path.
    * For `'regenerate_assistant_message'`: `origin_node_id` is the `node_id` of the *user message* for which the assistant's reply is being regenerated.
* **State Updates**: After API calls, the local `chat.value` (especially `message_nodes`, `entry_node_id`, and `active_next_node_id` on relevant nodes) must be updated accurately based on the backend response. The `updateChatStateFromResponse` function aims to centralize this. Merging new/updated nodes into `chat.value.message_nodes` correctly is important.
* **Error Handling**: Robustly inform the user about API errors.
* **Loading States**: Provide visual feedback during API calls (`isLoading`, `isSending`, etc.).
* **Chat List Management**: The provided solution focuses on a single active chat view. If you have a chat list component, it would use `chatApiService.getChats()` and `chatApiService.createChat()`, then pass the selected `activeChatId` as a prop to this detailed view component. Deleting a chat should refresh this list or navigate away.
* **`displayedMessages` Computed Property**: The logic for constructing this linear view from the graph should remain valid as long as `chat.value` (nodes and their `active_next_node_id` links) is correctly maintained.
* **UI for Edit/Regenerate**: Ensure your `UserMessage` and `LlmMessage` components have buttons or context menus to trigger the edit/regenerate actions and emit the corresponding events with the `node_id`.

This detailed frontend plan should provide a good starting point for an AI coding tool to implement the necessary changes. The logic for finding predecessor nodes for edits/regenerations is the most complex part and might need refinement based on exact UI interaction patterns.
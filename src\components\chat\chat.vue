<template>
  <div class="chat-root">
    <div class="title">{{ chat.title }}</div>
    <div class="messages">
      <template v-for="message in messages">
        <template v-if="message.role === 'user'">
          <UserMessage
            :key="message.node_id"
            :message="message.content"
            :versions="{ current: message.currentVersionIndex + 1, total: message.versionCount }"
            @change-version="handleVersionChange(message.node_id, $event)"
          />
        </template>
        <template v-else>
          <LlmMessage
            :key="message.node_id"
            :message="message.content"
            :versions="{ current: message.currentVersionIndex + 1, total: message.versionCount }"
            @change-version="handleVersionChange(message.node_id, $event)"
          />
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, ComputedRef, Reactive, reactive } from 'vue';
import LlmMessage from './llm-message.vue';
import UserMessage from './user-message.vue';

interface IChatProps {
  title: string;
}
interface DisplayMessage {
  node_id: string;
  role: string;
  content: string;
  versionCount: number;
  currentVersionIndex: number;
}

const props = defineProps<IChatProps>();
// const currentLeafBlockId = ref<string | null>("user_block_3_branch");

const messages: ComputedRef<DisplayMessage[]> = computed(() => {
  if (chat.message_nodes.length === 0)
    return [];
  let node = findNodeById(chat.entry_node_id);
  if (!node) {
    throw new Error('corrupted chat session');
  }

  const result: DisplayMessage[] = [];

  while (node !== undefined) {
    result.push({
      node_id: node.node_id,
      role: node.role,
      content: node.content,
      versionCount: node.next_node_ids.length,
      currentVersionIndex: node.next_node_ids.findIndex((v) => v === node.active_next_node_id),
    });
    if (node.next_node_ids.length === 0) // 叶子节点，跳出循环
      break;
    // 切到下一个节点
    node = findNodeById(node.active_next_node_id);

    if (node === undefined) {
      console.warn('corrupted chat session');
      break;
    }
  }
  return result;
});

function findNodeById(id: string) {
  return chat.message_nodes.find((block) => block.node_id === id);
}

function handleVersionChange(node_id: string, newVersionIndex: number) {
  console.log('handleVersionChange', node_id, newVersionIndex);
  const node = findNodeById(node_id);
  if (!node) {
    throw new Error('corrupted chat session');
  }
  node.active_next_node_id = node.next_node_ids[newVersionIndex - 1];
}


const chat: Reactive<ChatSession> = reactive({
  chat_id: "session_space_story_alpha_001",
  title: "太空探险故事 (完整示例)",
  creation_timestamp: "2024-07-28T10:00:00.000Z",
  last_update_timestamp: "2024-07-28T10:01:50.000Z",
  entry_node_id: "node_user_start_001",
  message_nodes: [
    {
      node_id: "node_user_start_001",
      role: "user",
      content: "你好，请给我讲一个关于太空旅行的短故事。",
      timestamp: "2024-07-28T10:00:00.000Z",
      metadata: {
        edit_source: "web_client_initial_prompt",
        custom_user_field: "some_value_for_user"
      } as MessageMetadata,
      next_node_ids: ["node_assistant_reply_001"],
      active_next_node_id: "node_assistant_reply_001",
    },
    {
      node_id: "node_assistant_reply_001",
      role: "assistant",
      content: "从前，在遥远的未来，一位名叫艾拉的年轻宇航员梦想着探索未知的星系。她登上了“星尘号”飞船，开始了一段惊心动魄的旅程...",
      timestamp: "2024-07-28T10:00:30.000Z",
      metadata: {
        model: "story-generator-v1",
        feedback: "neutral",
        generation_params: {
          "temperature": 0.7,
          "max_tokens": 150
        }
      } as MessageMetadata,
      next_node_ids: ["node_user_followup_002"],
      active_next_node_id: "node_user_followup_002",
    },
    {
      node_id: "node_user_followup_002",
      role: "user",
      content: "故事很棒！艾拉后来怎么样了？她遇到外星人了吗？",
      timestamp: "2024-07-28T10:01:00.000Z",
      metadata: {
        edit_source: "web_client_edit",
      } as MessageMetadata,
      next_node_ids: ["node_assistant_branch_A_003", "node_assistant_branch_B_004"],
      active_next_node_id: "node_assistant_branch_A_003", // 假设分支A是当前激活的路径
    },
    {
      node_id: "node_assistant_branch_A_003",
      role: "assistant",
      content: "艾拉的飞船进入了一个神秘的星云，在那里她遇到了一种友善的、由纯能量构成的外星生命体...",
      timestamp: "2024-07-28T10:01:45.000Z",
      metadata: {
        model: "story-generator-v2_alpha",
        feedback: "good",
      } as MessageMetadata,
      next_node_ids: [], // 叶子节点
      active_next_node_id: "", // 叶子节点，没有活动的下一节点
    },
    {
      node_id: "node_assistant_branch_B_004",
      role: "assistant",
      content: "不幸的是，艾拉的飞船遭遇了小行星带，通讯暂时中断了。她的命运悬而未决...",
      timestamp: "2024-07-28T10:01:50.000Z",
      metadata: {
        model: "story-generator-v2_beta_experimental",
      } as MessageMetadata,
      next_node_ids: ["1"], // 叶子节点
      active_next_node_id: "1", // 叶子节点，没有活动的下一节点
    },
    {
      node_id: "1",
      role: "user",
      content: "1111...",
      timestamp: "2024-07-28T10:01:50.000Z",
      metadata: {
        model: "story-generator-v2_beta_experimental",
      } as MessageMetadata,
      next_node_ids: [], // 叶子节点
      active_next_node_id: "", // 叶子节点，没有活动的下一节点
    }
  ],
});
</script>

<style lang="scss" scoped>
$title-height: 60px;

.chat-root {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.messages {
  display: flex;
  flex-direction: column;
  margin: auto;
  width: 100%;
  max-width: 780px;
  margin-top: $title-height + 10px;
}

.title {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: $title-height;
  line-height: $title-height;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
}
</style>
﻿<template>
  <div class="sidebar-folded">
    <div class="logo" @click="$emit('toggle')">
      <IconLogoDeepseek />
    </div>
    <div class="nav-item">
      <button @click="$emit('toggle')" tabindex="0" class="nav-icon">
        <IconExpandSidebar />
      </button>
    </div>
    <div class="nav-item">
      <button tabindex="0" class="nav-icon">
        <IconChat />
      </button>
    </div>
    <div class="flex-spacer"></div>
    <div class="mobile-btn">
      <IconMobile />
    </div>
    <div class="user-avatar" @click.stop="handleAvatarClick">
      <IconUser />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import IconExpandSidebar from '../icons/IconDocument.vue';
import IconChat from '../icons/IconChat.vue';
import IconMobile from '../icons/IconMobile.vue';
import IconUser from '../icons/IconUser.vue';
import IconLogoDeepseek from '../icons/IconLogoDeepseek.vue';
import IconLogout from '../icons/IconLogout.vue';
import usePopMenu from '../pop-menu/usePopMenu';
import { useUserStore } from '@/store/user';
import { useRouter } from 'vue-router';

defineEmits(['toggle']);

const userStore = useUserStore();
const router = useRouter();
const { toggle } = usePopMenu();

const userDisplayInfo = computed(() => {
  return userStore.phone_number || userStore.email || '未登录';
});

// 处理头像点击事件
function handleAvatarClick(e: PointerEvent) {
  // 动态创建菜单项以确保用户信息是最新的
  const AVATAR_MENU_ITEMS = [
    {
      id: 'user-info',
      title: userDisplayInfo.value,
      color: '#000000', // 黑色字体
      onClick: () => { console.log('User info clicked!'); }
    },
    {
      id: 'logout',
      icon: IconLogout,
      title: '退出登录',
      color: 'rgb(239, 68, 68)', // 红色字体
      onClick: () => {
        if (confirm('确定要退出登录吗？')) {
          /// @ts-expect-error
          userStore.logout();
          router.push('/login');
          console.log('User logged out!');
        }
      }
    }
  ];
  toggle(AVATAR_MENU_ITEMS, e.clientX, e.clientY);
}
</script>

<style scoped lang="scss">
$nav-item-spacing: 38px;

.sidebar-folded {
  align-items: center;
  background-color: rgb(249, 251, 255);
  display: flex;
  flex-direction: column;
  padding: 19px 0;
  width: 68px;
  height: 100%;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo {
  display: flex;
  width: 44px;
  height: 44px;
  cursor: pointer;
}

.nav-item {
  margin-top: $nav-item-spacing - 3px;
  padding: 3px;
  border-radius: 8px;

  .nav-icon {
    align-items: center;
    color: rgb(139, 139, 139);
    cursor: pointer;
    display: flex;
    justify-content: center;
    transition: background-color 0.2s ease;

    svg {
      width: 28px;
      height: 28px;
    }

  }
  &:hover {
    background-color: #eff1f5;
  }
}

.flex-spacer {
  flex: 1;
}

.mobile-btn {
  align-items: center;
  border-radius: 8px;
  color: rgb(163, 163, 163);
  cursor: pointer;
  display: flex;
  height: 36px;
  justify-content: center;
  margin-bottom: 22px;
  width: 36px;
  transition: background-color 0.2s ease;

  svg {
    width: 28px;
    height: 28px;
  }

  &:hover {
    background-color: #eff1f5;
  }
}

.user-avatar {
  align-items: center;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  height: 36px;
  justify-content: center;
  width: 36px;
  color: rgb(186, 186, 193);
  transition: background-color 0.2s ease;

  svg {
    width: 32px;
    height: 32px;
  }

  &:hover {
    background-color: #eff1f5;
  }
}
</style>
<script setup>
import ChatInputBox from "@/components/chat-initial/ChatInputBox.vue";
import IconLogoDeepseek from "./components/icons/IconLogoDeepseek.vue";
import Sidebar from "@/components/sidebar/sidebar.vue";
import Chat from "./components/chat/chat.vue";
</script>

<template>
  <div id="root">
    <Sidebar />
    <div class="chat-input-container" style="display: none;">
      <!-- <div class="chat-header">
        <div class="logo-wrapper">
          <IconLogoDeepseek />
        </div>
        我是 DeepSeek，很高兴见到你！
      </div>
      <div class="chat-content">
        我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~
      </div>
      <ChatInputBox style="width: 100%;" /> -->
    </div>
    <div class="chat-container">
      <Chat title="测试" />

    </div>
  </div>
</template>

<style scoped>
#root {
  display: flex;
  height: 100%;
  width: 100%;

}

.chat-input-container, .chat-container {
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  width: 100%;
  font-size: 14px;
  font-family: DeepSeek-CJK-patch, Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", Oxygen, "Open Sans", sans-serif;
}

.chat-input-container {
   max-width: 800px;
}

.chat-header {
  align-items: center;
  justify-content: center;
  gap: 14px;
  display: flex;
  font-size: 24px;
  font-weight: 500;
  flex-direction: row;
  line-height: 24px;
  width: 100%;
  max-width: 780px;
}

.logo-wrapper {
  display: flex;
  line-height: 0px;
  left: auto;
  position: static;
  font-size: 60px;
  width: 60px;
  height: 60px;
}

.logo-icon {
  height: 60px;
  width: 60px;
}

.chat-content {
  color: rgb(64, 64, 64);
  font-size: 14px;
  margin: 8px 0px 20px;
  line-height: 24px;
  width: 100%;
  max-width: 780px;
}



header {
  line-height: 1.5;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }
}
</style>
